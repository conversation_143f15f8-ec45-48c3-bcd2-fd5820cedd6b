export default function Logo({
  className = 'h-5 w-5 fill-smokyBrown',
  secondary,
  ...props
}: {
  className: React.HTMLAttributes<SVGElement>['className'];
  secondary?: boolean;
} & React.HTMLAttributes<SVGElement>) {
  return (
    <svg
      viewBox={secondary ? '0 0 24 24' : '0 0 20 20'}
      xmlns='http://www.w3.org/2000/svg'
      role='img'
      {...props}
      className={className}
    >
      {secondary ? (
        <path d='M1.04746 7.14483C-2.6686 15.343 4.04395 24.397 12.6693 23.9865C19.0171 23.6843 23.9911 18.3213 24 11.9962C24.0083 6.05205 19.8431 1.23091 13.7701 0.154927C8.61884 -0.757635 3.16997 2.46264 1.04746 7.14483ZM14.6979 3.38227C16.6932 3.88683 18.1243 4.50002 19.5612 5.9245C20.992 7.34298 21.2556 8.06814 21.2556 9.00578C21.2556 10.2572 20.4056 11.2141 19.1703 11.3534C18.2134 11.4614 17.5128 11.0992 16.7926 10.124C15.5108 8.38829 14.1717 7.65201 12.3156 7.66203C10.3332 7.67281 8.85124 8.46101 7.81422 10.056C6.0898 12.7084 7.04781 16.1915 9.90937 17.6742C11.0163 18.2476 12.9417 18.3365 14.1761 17.871C15.3164 17.441 16.481 16.3859 17.0107 15.3029C17.2398 14.8342 17.5872 14.346 17.7822 14.2181C18.3049 13.8756 19.0211 14.0805 19.3197 14.6582C19.5391 15.0827 19.5378 15.1941 19.3048 15.9165C18.9427 17.0395 18.4525 17.8255 17.5544 18.724C14.1449 22.1342 8.42969 21.1565 6.33254 16.8044C5.84398 15.7906 5.75788 15.4449 5.7009 14.2699C5.61896 12.579 5.93855 11.2833 6.74408 10.0412C7.84472 8.34422 9.55451 7.23188 11.6014 6.88123C13.521 6.5523 15.8138 7.23773 17.2318 8.56454C18.1426 9.41667 18.5586 9.57335 19.197 9.30497C20.3277 8.82937 20.1248 7.81239 18.6214 6.42027C17.6275 5.50004 16.5358 4.86961 15.0691 4.36844C13.889 3.96525 11.1736 3.96386 9.98638 4.36582C7.57841 5.18097 5.53486 6.89324 4.54019 8.92874C3.99265 10.0494 3.52566 11.7718 3.52011 12.6912C3.51719 13.1969 3.13521 13.7047 2.75786 13.7047C1.93483 13.7047 1.80842 12.9643 1.87594 12.3188C2.18259 9.38354 3.91133 6.55677 6.47501 4.79889C8.74574 3.24161 11.9549 2.68883 14.6979 3.38227Z' />
      ) : (
        <path d='M8.88671 0.103757C5.52719 0.685321 2.7219 2.70183 1.14822 5.6664C0.744141 6.42765 0.543352 6.94448 0.243256 7.9955C0.0166342 8.78934 -0.0762554 10.052 0.0715083 10.3307C0.320482 10.8003 0.853673 10.9953 1.34058 10.7949C1.8159 10.5992 1.93604 10.3505 2.00506 9.41951C2.09272 8.23741 2.34246 7.2955 2.84916 6.23644C4.86701 2.01881 9.88854 0.0806591 14.3252 1.80713C15.3646 2.21158 16.3144 2.8097 17.2774 3.66619C17.5314 3.89207 18.1492 4.64164 18.2611 4.85965C18.6181 5.55536 18.0867 6.37085 17.2738 6.37455C16.8817 6.37631 16.6916 6.26962 16.0942 5.71249C15.0145 4.70542 13.963 4.16362 12.4217 3.82005C11.8183 3.68559 10.38 3.68612 9.72967 3.82112C8.21685 4.13516 7.01336 4.76908 5.94834 5.81302C4.0043 7.71854 3.31851 10.4972 4.15973 13.0599C4.31647 13.5375 4.68999 14.3171 4.94984 14.7091C5.76904 15.9447 7.04872 16.975 8.44701 17.5248C9.80211 18.0575 11.6544 18.1521 13.0313 17.759C13.6795 17.5739 14.7144 17.0728 15.2915 16.6647C16.3945 15.8847 17.3796 14.6455 17.8473 13.4497C18.2969 12.3002 18.2695 11.7615 17.7381 11.3017C17.4689 11.0688 17.239 10.9845 16.873 10.9845C16.2969 10.9845 15.8629 11.3094 15.6069 11.9321C14.9535 13.5219 13.4967 14.7159 11.7931 15.058C8.8066 15.6578 5.99947 13.7637 5.48335 10.8005C5.38057 10.2104 5.44659 9.13285 5.62116 8.55038C5.88417 7.67305 6.30859 6.96404 6.95604 6.32042C7.58419 5.69593 8.22664 5.31608 9.18582 5.00188C9.62977 4.85643 10.0286 4.74276 10.741 4.74276C11.4535 4.74276 11.7862 4.85611 12.2314 5.00311C13.5372 5.4343 14.4616 6.13178 15.2083 7.24936C15.8097 8.14963 16.5274 8.57294 17.4523 8.57294C18.6394 8.57294 19.7131 7.72063 19.961 6.58146C19.9971 6.41581 20.01 6.10054 19.9919 5.82615C19.9544 5.25531 19.8297 4.96013 19.3455 4.29652C18.9337 3.732 17.6876 2.48645 17.1254 2.07739C15.6698 1.01828 14.0886 0.361741 12.3171 0.0808734C11.5762 -0.0366507 9.62232 -0.0235746 8.88671 0.103757Z' />
      )}
    </svg>
  );
}
