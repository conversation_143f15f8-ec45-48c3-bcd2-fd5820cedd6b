# Use the official Node.js 18 image as base
FROM node:18-alpine

# Set working directory inside container
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm

# Copy only package.json first (ignore lockfile to regenerate it)
COPY package.json ./

# Clean install - this will generate a new lockfile
RUN pnpm install

# Copy the rest of the application code
COPY . .

# Create a non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Change ownership of the app directory to the nextjs user
RUN chown -R nextjs:nodejs /app
USER nextjs

# Expose the port the app runs on
EXPOSE 3000

# Set environment to development
ENV NODE_ENV=development

# Start the development server
CMD ["pnpm", "run", "dev"]
