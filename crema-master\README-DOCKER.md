# 🐳 Ejecutar Crema con Docker (Modo <PERSON>ro)

Este setup de Docker te permite ejecutar el proyecto Crema de forma completamente aislada de tu sistema, proporcionando una capa adicional de seguridad.

## 🛡️ Características de Seguridad

- **Contenedor aislado**: La aplicación se ejecuta completamente separada de tu sistema
- **Usuario no-root**: El contenedor usa un usuario sin privilegios
- **Red aislada**: Comunicación solo a través de puertos específicos
- **Sin acceso al sistema de archivos**: Solo acceso a los archivos del proyecto

## 📋 Requisitos Previos

1. **Docker Desktop** instalado en tu sistema
   - Windows: [Descargar Docker Desktop](https://www.docker.com/products/docker-desktop)
   - Asegúrate de que Docker esté ejecutándose

## 🚀 Ejecución Rápida

### Opción 1: Script Automático (Recomendado)
```bash
# Ejecuta el script que hace todo automáticamente
docker-run.bat
```

### Opción 2: Comandos Manuales
```bash
# 1. Construir la imagen Docker
docker-compose build

# 2. Ejecutar el contenedor
docker-compose up

# 3. Abrir en el navegador
# http://localhost:3000

# 4. Para detener (Ctrl+C o en otra terminal)
docker-compose down
```

## 🔧 Comandos Útiles

```bash
# Ver contenedores ejecutándose
docker ps

# Ver logs del contenedor
docker-compose logs -f

# Ejecutar comandos dentro del contenedor
docker-compose exec crema-app sh

# Limpiar todo (imágenes, contenedores, volúmenes)
docker system prune -a
```

## 🌐 Acceso a la Aplicación

Una vez iniciado, el proyecto estará disponible en:
- **URL**: http://localhost:3000
- **Puerto**: 3000

## 🛑 Detener la Aplicación

- **Método 1**: Presiona `Ctrl+C` en la terminal donde se ejecuta
- **Método 2**: Ejecuta `docker-compose down` en otra terminal

## 🔒 Configuración de Seguridad

El contenedor incluye las siguientes medidas de seguridad:

- ✅ Usuario no-root (UID: 1001)
- ✅ Red aislada
- ✅ Sin privilegios adicionales
- ✅ Filesystem de solo lectura para áreas críticas
- ✅ Tmpfs para archivos temporales

## 🐛 Solución de Problemas

### Error: "Docker no está instalado"
- Instala Docker Desktop desde el enlace oficial
- Reinicia tu terminal después de la instalación

### Error: "Docker no está ejecutándose"
- Abre Docker Desktop
- Espera a que aparezca el ícono verde en la bandeja del sistema

### Error: "Puerto 3000 en uso"
- Detén cualquier aplicación que use el puerto 3000
- O cambia el puerto en `docker-compose.yml`: `"3001:3000"`

### El proyecto no carga
- Verifica que Docker esté ejecutándose: `docker ps`
- Revisa los logs: `docker-compose logs`
- Asegúrate de que el puerto 3000 esté libre

## 📝 Notas Importantes

- Los cambios en el código se reflejan automáticamente (hot reload)
- Los `node_modules` se instalan dentro del contenedor
- No se requiere tener Node.js instalado en tu máquina
- El contenedor se elimina automáticamente al detenerlo
