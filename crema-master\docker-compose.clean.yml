version: '3.8'

services:
  crema-app:
    build:
      context: .
      dockerfile: Dockerfile.clean
    ports:
      - "3000:3000"
    volumes:
      # Mount source code for hot reload (optional - remove for production)
      - .:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_GOOGLE_ANALYTICS=${NEXT_PUBLIC_GOOGLE_ANALYTICS:-}
      - NEXT_PUBLIC_MS_CLARITY=${NEXT_PUBLIC_MS_CLARITY:-}
      - NEXT_PUBLIC_IUBENDA_SITE_ID=${NEXT_PUBLIC_IUBENDA_SITE_ID:-}
      - NEXT_PUBLIC_IUBENDA_COOKIE_POLICY_ID=${NEXT_PUBLIC_IUBENDA_COOKIE_POLICY_ID:-}
    networks:
      - crema-network
    restart: unless-stopped
    # Security settings
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp
    user: "1001:1001"

networks:
  crema-network:
    driver: bridge
