{"name": "crema-cioccolato-bt-v2", "version": "2.2.0", "private": true, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/federicofrasca796/"}, "engines": {"node": ">=18.17.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --check .", "format:fix": "prettier --write --list-different .", "prepare": "husky install", "commit": "cz"}, "dependencies": {"@heroicons/react": "^2.1.1", "clsx": "^2.0.0", "csv-parser": "^3.0.0", "motion": "^11.12.0", "next": "^14.2.3", "react": "^18", "react-dom": "^18", "slugify": "^1.6.6", "swiper": "^11.0.5"}, "devDependencies": {"@tailwindcss/typography": "^0.5.10", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "axios": "^1.4.0", "commitizen": "^4.3.0", "cz-emoji-conventional": "^1.0.2", "daisyui": "^4.5.0", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "execp": "^0.0.1", "fs": "^0.0.1-security", "husky": "^8.0.0", "lint-staged": "^15.2.0", "path": "^0.12.7", "postcss": "^8", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "request": "^2.88.2", "tailwindcss": "^3.4.0", "typescript": "^5"}, "config": {"commitizen": {"path": "node_modules/cz-emoji-conventional"}}}