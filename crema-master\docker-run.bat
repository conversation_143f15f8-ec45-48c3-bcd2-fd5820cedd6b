@echo off
echo ========================================
echo    CREMA PROJECT - DOCKER SETUP
echo ========================================
echo.

echo Verificando si Docker esta instalado...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker no esta instalado o no esta en el PATH
    echo Por favor instala Docker Desktop desde: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo Docker encontrado!
echo.

echo Verificando si Docker esta ejecutandose...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker no esta ejecutandose
    echo Por favor inicia Docker Desktop
    pause
    exit /b 1
)

echo Docker esta ejecutandose!
echo.

echo ========================================
echo Construyendo la imagen Docker...
echo ========================================
docker-compose build

if %errorlevel% neq 0 (
    echo ERROR: Fallo al construir la imagen Docker
    pause
    exit /b 1
)

echo.
echo ========================================
echo Iniciando el contenedor...
echo ========================================
echo El proyecto estara disponible en: http://localhost:3000
echo Presiona Ctrl+C para detener el servidor
echo.

docker-compose up

echo.
echo ========================================
echo Limpiando contenedores...
echo ========================================
docker-compose down

echo.
echo Proyecto detenido. Presiona cualquier tecla para salir...
pause >nul
